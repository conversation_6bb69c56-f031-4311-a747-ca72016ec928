<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\RiderPreferencesRequest;
use App\Http\Resources\RecentRidesCollection;
use App\Http\Resources\RiderCollection;
use App\Http\Resources\RiderResource;
use App\Http\Responses\ApiResponse;
use App\Models\AddressLabel;
use App\Models\Rider;
use App\Models\RiderPreferences;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class RiderController extends Controller
{
    /**
     * Home
     *
     * @response array{userAddresses : RiderCollection,recent : RecentRidesCollection , message: string, status: integer,error : string}
     */
    public function home()
    {
        $user = Auth::user()->load(['rider' => function ($query) {
            return $query->withTrashed();
        }]);

        if ($user->type !== 'passenger' || ! $user->rider) {
            return ApiResponse::error('', 'Unauthorized access', 403, null);
        }

        $addresses = $user->address()
            ->where('address_label_id', '!=', null)
            ->where('shortcut', true)
            ->with('label')
            ->orderBy('shortcut_created_at', 'asc')
            ->paginate(20);

        $data = [
            'userAddresses' => new RiderCollection($addresses),
        ];

        return ApiResponse::success($data, 'User data has been loaded successfully.', 200, null);
    }

    /**
     * Add new favorite address
     *
     * Adds a new address to the rider's favorite addresses list.
     *
     * @response array{data :array , message: string, status: integer,error : string}
     **/
    public function addFavoriteAddress(Request $request)
    {
        $rider = Auth::user()->load('rider');
        if ($rider->type !== 'passenger') {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        // Check if the rider already has 10 shortcut addresses
        $shortcutCount = \App\Models\Address::where('addressable_type', 'App\Models\User')
            ->where('addressable_id', $rider->id)
            ->where('shortcut', true)
            ->count();

        if ($shortcutCount >= 10) {
            return ApiResponse::error(null, 'You can have a maximum of 10 shortcut addresses', 400);
        }

        $validated = $request->validate([
            'address' => [
                'required',
                'string',
                new \App\Rules\UniqueShortcut($rider->id, null, 'address'),
            ],
            'full_address' => 'sometimes|string',
            'postal_address' => 'sometimes|json',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'icon' => 'required|string',
            'label' => [
                'required',
                'string',
                'max:20',
                new \App\Rules\UniqueShortcut($rider->id, null, 'label'),
            ],
        ]);

        // Create the address first
        $address = $rider->address()->create([
            'address' => $validated['address'],
            'full_address' => $validated['full_address'] ?? null,
            'postal_address' => $validated['postal_address'] ?? null,
            'latitude' => $validated['latitude'],
            'longitude' => $validated['longitude'],
            'is_favorite' => true,
            'shortcut' => true, // Mark as shortcut since this is addFavoriteAddress
        ]);

        if ($address) {
            // Create the address label and associate it with the address
            $addressLabel = \App\Models\AddressLabel::create([
                'label' => $validated['label'],
                'icon' => $validated['icon'],
                'address_id' => $address->id,
            ]);

            // Update the address with the label ID
            $address->address_label_id = $addressLabel->id;
            $address->save();

            // Reload the address with its label
            $address->load('label');

            return ApiResponse::success($address, 'address added to favorite successfully', 201, null);
        } else {
            return ApiResponse::success(null, 'failed to add to favorite', 500, null);
        }
    }

    /**
     * Rider preferences
     *
     * @response array{data : RiderResource , message: string, status: integer,error : string}
     **/
    public function preferences(RiderPreferencesRequest $request)
    {
        $validated = $request->validated();

        $preference = RiderPreferences::updateOrCreate(
            ['user_id' => Auth::user()->id],
            [
                'driver_gender' => Auth::user()->gender == 'female' ? $validated['driver_gender'] : 'both',
                'seats_number' => $validated['seats_number'],
            ]
        );

        $preference->carEquipments()->sync($validated['car_equipments']);
        $preference->carEquipments()->sync(
            collect($validated['car_equipments'])->mapWithKeys(fn ($id) => [$id => ['created_at' => now(), 'updated_at' => now()]])
        );

        $preference->carTypes()->sync(
            collect($validated['car_types'])->mapWithKeys(fn ($id) => [$id => ['created_at' => now(), 'updated_at' => now()]])
        );

        $preference->load(['carTypes', 'carEquipments']);

        $message = $preference->wasRecentlyCreated
        ? 'Rider preferences created successfully.'
        : 'Rider preferences updated successfully.';

        return ApiResponse::success(new RiderResource($preference), $message, 200, null);
    }

    /**
     * Rider heartbeat
     *
     * @response array{data : null , message: 'Rider heartbeat updated', status: 200,error : string}
     **/
    public function heartbeat(Request $request)
    {
        $user = Auth::user();

        if (! $user->rider) {
            return ApiResponse::error(null, 'User is not a rider', 403);
        }

        // Update rider's heartbeat
        $user->rider->update(['last_heartbeat' => now()]);

        Log::info('Rider heartbeat updated', [
            'rider_id' => $user->rider->id,
            'timestamp' => now(),
        ]);

        return ApiResponse::success(null, 'Heartbeat updated');
    }

    public function updateLabel(Request $request)
    {
        $addressId = $request->input('address_id');
        $userId = Auth::id();

        $validated = $request->validate([
            'address_id' => 'required|numeric|exists:addresses,id',
            'address' => [
                'sometimes',
                'string',
                new \App\Rules\UniqueShortcut($userId, $addressId, 'address'),
            ],
            'latitude' => 'sometimes|numeric',
            'longitude' => 'sometimes|numeric',
            'label' => [
                'sometimes',
                'string',
                'max:20',
                new \App\Rules\UniqueShortcut($userId, $addressId, 'label'),
            ],
            'icon' => 'sometimes|string',
        ]);

        // Check if user owns this address
        $userAddress = Auth::user()->address()->where('id', $validated['address_id'])->first();
        if (! $userAddress) {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        // Check if at least one field is being updated
        if (! isset($validated['address']) && ! isset($validated['label']) && ! isset($validated['icon'])) {
            return ApiResponse::error(null, 'No fields to update', 400);
        }

        // Update address details if provided
        if (isset($validated['address'])) {
            $updateData = ['address' => $validated['address']];

            // Only update coordinates if they are provided
            if (isset($validated['latitude'])) {
                $updateData['latitude'] = $validated['latitude'];
            }
            if (isset($validated['longitude'])) {
                $updateData['longitude'] = $validated['longitude'];
            }

            $userAddress->update($updateData);
        }

        // Update or create address label
        $addressLabel = AddressLabel::updateOrCreate(
            ['address_id' => $validated['address_id']],
            [
                'label' => $validated['label'] ?? 'Default',
                'icon' => $validated['icon'] ?? 'heroicon-c-home',
            ]
        );
        $addressLabel->address->update(['address_label_id' => $addressLabel->id]);
        $message = $addressLabel->wasRecentlyCreated
            ? 'Label created successfully'
            : 'Label updated successfully';

        return ApiResponse::success($addressLabel->label, $message, $addressLabel->wasRecentlyCreated ? 201 : 200, null);
    }

    public function removeLabel($addressId)
    {
        $user = Auth::user();

        if (! $user->address()->where('id', $addressId)->exists()) {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        // Set shortcut to false for the address
        \App\Models\Address::where('id', $addressId)
            ->where('addressable_id', $user->id)
            ->update(['shortcut' => false]);

        AddressLabel::where('address_id', $addressId)->delete();

        return ApiResponse::success(null, 'Label removed successfully', 200, null);
    }

    /**
     * Set address shortcut
     *
     * Sets the shortcut flag to true for the specified address
     *
     * @response array{data: array, message: string, status: integer, error: string}
     **/
    public function setAddressShortcut($addressId)
    {
        if (Auth::user()->type !== 'passenger') {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        $address = \App\Models\Address::where('id', $addressId)
            ->where('addressable_id', Auth::user()->id)
            ->with('label')
            ->first();

        if (! $address) {
            return ApiResponse::error(null, 'Address not found or does not belong to you', 404);
        }

        // Only check the 10-shortcut limit if the address is not already a shortcut
        if (! $address->shortcut) {
            $shortcutCount = \App\Models\Address::where('addressable_type', 'App\Models\User')
                ->where('addressable_id', Auth::user()->id)
                ->where('shortcut', true)
                ->count();

            if ($shortcutCount >= 10) {
                return ApiResponse::error(null, 'You can have a maximum of 10 shortcut addresses', 400);
            }
        }

        // Check for duplicate address among existing shortcuts
        $duplicateAddress = \App\Models\Address::where('addressable_type', 'App\Models\User')
            ->where('addressable_id', Auth::user()->id)
            ->where('shortcut', true)
            ->where('id', '!=', $addressId)
            ->where(function ($query) use ($address) {
                $query->where('address', $address->address);

                // Also check coordinates with small tolerance for GPS precision
                if ($address->latitude && $address->longitude) {
                    $query->orWhere(function ($coordQuery) use ($address) {
                        $coordQuery->whereBetween('latitude', [$address->latitude - 0.0001, $address->latitude + 0.0001])
                            ->whereBetween('longitude', [$address->longitude - 0.0001, $address->longitude + 0.0001]);
                    });
                }
            })
            ->exists();

        if ($duplicateAddress) {
            return ApiResponse::error(null, 'A shortcut for this address already exists', 409);
        }

        // Check for duplicate label among existing shortcuts if address has a label
        if ($address->label) {
            $duplicateLabel = \App\Models\AddressLabel::whereHas('address', function ($addressQuery) use ($address) {
                $addressQuery->where('addressable_type', 'App\Models\User')
                    ->where('addressable_id', Auth::user()->id)
                    ->where('shortcut', true)
                    ->where('id', '!=', $address->id);
            })->where('label', $address->label->label)->exists();

            if ($duplicateLabel) {
                return ApiResponse::error(null, 'A shortcut with this label already exists', 409);
            }
        }

        // If address is already a shortcut, return success without changes
        if ($address->shortcut) {
            return ApiResponse::success($address, 'Address is already a shortcut', 200, null);
        }

        // Update the shortcut flag
        $address->shortcut = true;
        $address->save();

        // Return the updated address
        return ApiResponse::success($address, 'Address shortcut set successfully', 200, null);
    }

    /**
     * Remove favorite address
     *
     * Sets both the is_favorite and shortcut flags to false for the specified address
     * When a favorite address is deleted, any shortcut linked to that address should also be deleted automatically
     *
     * @response array{data: array, message: string, status: integer, error: string}
     **/
    public function removeFavoriteAddress($addressId)
    {
        if (Auth::user()->type !== 'passenger') {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        $address = \App\Models\Address::where('id', $addressId)
            ->where('addressable_id', Auth::user()->id)
            ->first();

        if (! $address) {
            return ApiResponse::error(null, 'Address not found or does not belong to you', 404);
        }

        // Update both is_favorite and shortcut flags to false
        // When a favorite address is deleted, any linked shortcut should also be deleted
        $address->is_favorite = false;
        $address->shortcut = false;
        $address->save();

        // Return the updated address
        return ApiResponse::success($address, 'Favorite address removed successfully', 200, null);
    }

    /**
     * Get favorite address icons
     *
     * Returns all addresses that are marked as shortcuts (favorites) with their labels and SVG icons
     *
     * @response array{data: array, message: string, status: integer, error: string}
     **/
    public function getFavoriteAddressIcons()
    {
        if (Auth::user()->type !== 'passenger') {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        // Get all shortcut addresses with their labels for the authenticated user
        $favoriteAddresses = \App\Models\Address::where('addressable_type', 'App\Models\User')
            ->where('addressable_id', Auth::user()->id)
            ->where('shortcut', true)
            ->whereNotNull('address_label_id')
            ->with(['label.availableIcon'])
            ->orderBy('shortcut_created_at', 'asc')
            ->get();

        // Format the response data
        $formattedAddresses = $favoriteAddresses->map(function ($address) {
            return [
                'id' => $address->id,
                'address' => $address->address,
                'full_address' => $address->full_address,
                'latitude' => $address->latitude,
                'longitude' => $address->longitude,
                'label' => $address->label ? $address->label->label : null,
                'icon_name' => $address->label ? $address->label->getIconNameAttribute() : null,
                'icon_svg' => $address->label ? $address->label->getIconSvgAttribute() : null,
                'shortcut' => $address->shortcut,
                'created_at' => $address->created_at,
                'updated_at' => $address->updated_at,
            ];
        });

        return ApiResponse::success($formattedAddresses, 'Favorite address icons retrieved successfully', 200, null);
    }
}
