<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Address extends Model
{
    use HasFactory;

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($address) {
            // Set shortcut_created_at when shortcut is set to true for the first time
            if ($address->shortcut && ($address->isDirty('shortcut') || ! $address->exists) && ! $address->getOriginal('shortcut')) {
                $address->shortcut_created_at = now();
            }
            // Clear shortcut_created_at when shortcut is set to false
            elseif (! $address->shortcut && $address->isDirty('shortcut')) {
                $address->shortcut_created_at = null;
            }
        });
    }

    protected $fillable = [
        'id',
        'addressable_type',
        'addressable_id',
        'address',
        'full_address',
        'postal_address',
        'latitude',
        'longitude',
        'address_label_id',
        'is_favorite',
        'shortcut',
        'shortcut_created_at',
    ];

    /**
     * Cast JSON fields for easier manipulation.
     */
    protected $casts = [
        'postal_address' => 'array',
        'geocode' => 'array',
        'is_favorite' => 'boolean',
        'shortcut' => 'boolean',
        'shortcut_created_at' => 'datetime',
    ];

    /**
     * Get the parent model of the address (e.g., User, Trip, etc.).
     */
    public function addressable(): MorphTo
    {
        return $this->morphTo();
    }

    public function label()
    {
        return $this->belongsTo(AddressLabel::class, 'address_label_id');
    }

    // public function tripsAsDeparture()
    // {
    //     return $this->hasMany(Trip::class, 'departure_address_id');
    // }

    // public function tripsAsArrival()
    // {
    //     return $this->hasMany(Trip::class, 'arrival_address_id');
    // }
}
