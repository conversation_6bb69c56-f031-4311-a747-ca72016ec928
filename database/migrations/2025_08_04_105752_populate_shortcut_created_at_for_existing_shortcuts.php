<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing shortcut addresses to set shortcut_created_at
        // Use updated_at as a reasonable approximation for when they became shortcuts
        DB::table('addresses')
            ->where('shortcut', true)
            ->whereNull('shortcut_created_at')
            ->update([
                'shortcut_created_at' => DB::raw('updated_at'),
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Clear shortcut_created_at for all addresses
        DB::table('addresses')
            ->whereNotNull('shortcut_created_at')
            ->update([
                'shortcut_created_at' => null,
            ]);
    }
};
